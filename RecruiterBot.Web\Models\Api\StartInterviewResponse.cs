using RecruiterBot.Core.Models;

namespace RecruiterBot.Web.Models.Api
{
    public class StartInterviewResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public InterviewSessionInfo Interview { get; set; }
    }

    public class InterviewSessionInfo
    {
        public string Id { get; set; }
        public string JobDescription { get; set; }
        public DateTime InterviewDateTimeUtc { get; set; }
        public DateTime InterviewDateTimeLocal { get; set; }
        public string TimeZone { get; set; }
        public InterviewStatus Status { get; set; }
        public string StatusDisplayName { get; set; }
        public string Notes { get; set; }
        public DateTime CreatedDateUtc { get; set; }
        public DateTime? SessionStartedDateUtc { get; set; }
        public string SessionStartedBy { get; set; }
        public InterviewType InterviewType { get; set; }
        public string InterviewTypeDisplayName { get; set; }
        
        // Candidate information
        public CandidateProfile Candidate { get; set; }
        
        // LLM Model information
        public LLMModelProfile LLMModel { get; set; }
    }

    public class CandidateProfile
    {
        public string Id { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string FullName => $"{FirstName} {LastName}";
        public string Email { get; set; }
        public string PhoneNumber { get; set; }
        public List<string> Skills { get; set; } = new List<string>();
        public int? YearsOfExperience { get; set; }
        public string CurrentJobTitle { get; set; }
        public string CurrentCompany { get; set; }
        public string Summary { get; set; }
        public List<WorkExperience> WorkExperience { get; set; } = new List<WorkExperience>();
        public List<Education> Education { get; set; } = new List<Education>();
        public List<string> Certifications { get; set; } = new List<string>();
    }

    public class LLMModelProfile
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string DisplayName { get; set; }
        public string Provider { get; set; }
        public string Description { get; set; }
        public ModelType ModelType { get; set; }
        public int MaxTokens { get; set; }
    }
}
