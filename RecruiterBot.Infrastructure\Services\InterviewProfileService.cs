using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MongoDB.Driver;
using RecruiterBot.Core.Models;
using RecruiterBot.Infrastructure.Data;

namespace RecruiterBot.Infrastructure.Services
{
    public class InterviewProfileService : IInterviewProfileService
    {
        private readonly IMongoCollection<InterviewProfile> _interviewProfiles;
        private readonly ILogger<InterviewProfileService> _logger;
        private const string CollectionName = "InterviewProfiles";

        public InterviewProfileService(
            IOptions<MongoDbSettings> settings,
            ILogger<InterviewProfileService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            if (settings?.Value == null)
                throw new ArgumentNullException(nameof(settings));

            var mongoClient = new MongoClient(settings.Value.ConnectionString);
            var database = mongoClient.GetDatabase(settings.Value.DatabaseName);
            _interviewProfiles = database.GetCollection<InterviewProfile>(CollectionName);

            // Create indexes for better performance
            CreateIndexes();
        }

        public async Task<IEnumerable<InterviewProfile>> GetProfilesByConsultantAsync(string consultantId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(consultantId))
                    return Enumerable.Empty<InterviewProfile>();

                var profiles = await _interviewProfiles
                    .Find(p => p.CreatedBy == consultantId)
                    .SortByDescending(p => p.UpdatedAt)
                    .ToListAsync();

                _logger.LogInformation("Retrieved {Count} interview profiles for consultant {ConsultantId}", 
                    profiles.Count, consultantId);

                return profiles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving interview profiles for consultant {ConsultantId}", consultantId);
                throw;
            }
        }

        public async Task<InterviewProfile?> GetProfileByIdAsync(string id, string consultantId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(id) || string.IsNullOrWhiteSpace(consultantId))
                    return null;

                var profile = await _interviewProfiles
                    .Find(p => p.Id == id && p.CreatedBy == consultantId)
                    .FirstOrDefaultAsync();

                if (profile != null)
                {
                    _logger.LogInformation("Retrieved interview profile {ProfileId} for consultant {ConsultantId}", 
                        id, consultantId);
                }

                return profile;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving interview profile {ProfileId} for consultant {ConsultantId}", 
                    id, consultantId);
                throw;
            }
        }

        public async Task<InterviewProfile> CreateProfileAsync(InterviewProfile profile, string consultantId)
        {
            try
            {
                if (profile == null)
                    throw new ArgumentNullException(nameof(profile));

                if (string.IsNullOrWhiteSpace(consultantId))
                    throw new ArgumentException("Consultant ID cannot be empty", nameof(consultantId));

                // Validate the profile
                if (!profile.IsValid())
                    throw new InvalidOperationException("Profile data is not valid");

                // Set audit fields
                profile.CreatedBy = consultantId;
                profile.Tenant = consultantId; // For consultant profiles, tenant is the consultant themselves
                profile.CreatedAt = DateTime.UtcNow;
                profile.UpdatedAt = DateTime.UtcNow;

                // Clean and validate data
                CleanProfileData(profile);

                await _interviewProfiles.InsertOneAsync(profile);

                _logger.LogInformation("Created interview profile {ProfileId} for consultant {ConsultantId}", 
                    profile.Id, consultantId);

                return profile;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating interview profile for consultant {ConsultantId}", consultantId);
                throw;
            }
        }

        public async Task<InterviewProfile?> UpdateProfileAsync(InterviewProfile profile, string consultantId)
        {
            try
            {
                if (profile == null)
                    throw new ArgumentNullException(nameof(profile));

                if (string.IsNullOrWhiteSpace(consultantId))
                    throw new ArgumentException("Consultant ID cannot be empty", nameof(consultantId));

                // Verify the profile exists and belongs to the consultant
                var existingProfile = await GetProfileByIdAsync(profile.Id, consultantId);
                if (existingProfile == null)
                {
                    _logger.LogWarning("Attempted to update non-existent or unauthorized profile {ProfileId} by consultant {ConsultantId}", 
                        profile.Id, consultantId);
                    return null;
                }

                // Validate the updated profile
                if (!profile.IsValid())
                    throw new InvalidOperationException("Profile data is not valid");

                // Preserve creation info and update audit fields
                profile.CreatedBy = existingProfile.CreatedBy;
                profile.Tenant = existingProfile.Tenant;
                profile.CreatedAt = existingProfile.CreatedAt;
                profile.UpdatedAt = DateTime.UtcNow;

                // Clean and validate data
                CleanProfileData(profile);

                await _interviewProfiles.ReplaceOneAsync(p => p.Id == profile.Id && p.CreatedBy == consultantId, profile);

                _logger.LogInformation("Updated interview profile {ProfileId} for consultant {ConsultantId}", 
                    profile.Id, consultantId);

                return profile;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating interview profile {ProfileId} for consultant {ConsultantId}", 
                    profile?.Id, consultantId);
                throw;
            }
        }

        public async Task<bool> DeleteProfileAsync(string id, string consultantId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(id) || string.IsNullOrWhiteSpace(consultantId))
                    return false;

                var result = await _interviewProfiles.DeleteOneAsync(p => p.Id == id && p.CreatedBy == consultantId);

                if (result.DeletedCount > 0)
                {
                    _logger.LogInformation("Deleted interview profile {ProfileId} for consultant {ConsultantId}", 
                        id, consultantId);
                    return true;
                }

                _logger.LogWarning("Attempted to delete non-existent or unauthorized profile {ProfileId} by consultant {ConsultantId}", 
                    id, consultantId);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting interview profile {ProfileId} for consultant {ConsultantId}", 
                    id, consultantId);
                throw;
            }
        }

        public async Task<IEnumerable<InterviewProfile>> SearchProfilesAsync(string searchTerm, string consultantId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(consultantId))
                    return Enumerable.Empty<InterviewProfile>();

                if (string.IsNullOrWhiteSpace(searchTerm))
                    return await GetProfilesByConsultantAsync(consultantId);

                // For now, use a simple text search approach
                var allProfiles = await _interviewProfiles
                    .Find(p => p.CreatedBy == consultantId)
                    .ToListAsync();

                var filteredProfiles = allProfiles.Where(profile =>
                    profile.AboutYou.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                    (!string.IsNullOrWhiteSpace(profile.PrimaryTechnology) && profile.PrimaryTechnology.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) ||
                    profile.Experiences.Any(exp =>
                        exp.CompanyName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                        exp.ProjectDescription.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) ||
                    profile.Achievements.Any(ach => ach.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
                ).OrderByDescending(p => p.UpdatedAt).ToList();

                _logger.LogInformation("Found {Count} interview profiles matching search '{SearchTerm}' for consultant {ConsultantId}",
                    filteredProfiles.Count, searchTerm, consultantId);

                return filteredProfiles;


            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching interview profiles for consultant {ConsultantId} with term '{SearchTerm}'", 
                    consultantId, searchTerm);
                throw;
            }
        }

        public async Task<int> GetProfileCountAsync(string consultantId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(consultantId))
                    return 0;

                var count = await _interviewProfiles.CountDocumentsAsync(p => p.CreatedBy == consultantId);
                return (int)count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting profile count for consultant {ConsultantId}", consultantId);
                throw;
            }
        }

        public async Task<bool> CanConsultantAccessProfileAsync(string profileId, string consultantId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(profileId) || string.IsNullOrWhiteSpace(consultantId))
                    return false;

                var count = await _interviewProfiles.CountDocumentsAsync(p => p.Id == profileId && p.CreatedBy == consultantId);
                return count > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking access for profile {ProfileId} by consultant {ConsultantId}", 
                    profileId, consultantId);
                return false;
            }
        }

        public async Task<string?> GetFormattedProfileForAIAsync(string profileId, string consultantId)
        {
            try
            {
                var profile = await GetProfileByIdAsync(profileId, consultantId);
                return profile?.GetFormattedProfileForAI();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting formatted profile {ProfileId} for AI by consultant {ConsultantId}", 
                    profileId, consultantId);
                return null;
            }
        }

        private void CreateIndexes()
        {
            try
            {
                // Create index on CreatedBy for tenant isolation
                var createdByIndex = Builders<InterviewProfile>.IndexKeys.Ascending(p => p.CreatedBy);
                _interviewProfiles.Indexes.CreateOne(new CreateIndexModel<InterviewProfile>(createdByIndex));

                // Create compound index on CreatedBy and UpdatedAt for sorting
                var sortIndex = Builders<InterviewProfile>.IndexKeys
                    .Ascending(p => p.CreatedBy)
                    .Descending(p => p.UpdatedAt);
                _interviewProfiles.Indexes.CreateOne(new CreateIndexModel<InterviewProfile>(sortIndex));

                // Create text index for search functionality
                var textIndex = Builders<InterviewProfile>.IndexKeys
                    .Text(p => p.AboutYou)
                    .Text(p => p.PrimaryTechnology)
                    .Text(p => p.Achievements);
                _interviewProfiles.Indexes.CreateOne(new CreateIndexModel<InterviewProfile>(textIndex));

                _logger.LogInformation("Created indexes for InterviewProfile collection");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error creating indexes for InterviewProfile collection");
                // Don't throw - indexes are not critical for basic functionality
            }
        }

        private static void CleanProfileData(InterviewProfile profile)
        {
            // Clean and trim text fields
            profile.AboutYou = profile.AboutYou?.Trim() ?? string.Empty;

            // Clean primary technology
            profile.PrimaryTechnology = profile.PrimaryTechnology?.Trim() ?? string.Empty;

            // Clean achievements
            profile.Achievements = profile.Achievements?
                .Where(achievement => !string.IsNullOrWhiteSpace(achievement))
                .Select(achievement => achievement.Trim())
                .ToList() ?? new List<string>();

            // Clean experiences
            if (profile.Experiences != null)
            {
                foreach (var experience in profile.Experiences)
                {
                    experience.CompanyName = experience.CompanyName?.Trim() ?? string.Empty;
                    experience.ProjectDescription = experience.ProjectDescription?.Trim() ?? string.Empty;
                    
                    experience.TechnologiesUsed = experience.TechnologiesUsed?
                        .Where(tech => !string.IsNullOrWhiteSpace(tech))
                        .Select(tech => tech.Trim())
                        .Distinct(StringComparer.OrdinalIgnoreCase)
                        .ToList() ?? new List<string>();
                }

                // Remove invalid experiences
                profile.Experiences = profile.Experiences
                    .Where(exp => exp.IsValid())
                    .OrderByDescending(exp => exp.StartDate)
                    .ToList();
            }
        }
    }
}
