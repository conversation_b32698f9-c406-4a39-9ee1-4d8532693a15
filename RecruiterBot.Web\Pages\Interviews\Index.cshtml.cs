using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Logging;
using RecruiterBot.Infrastructure.Services;
using RecruiterBot.Core.Models;
using RecruiterBot.Core.Constants;
using System.Security.Claims;

namespace RecruiterBot.Web.Pages.Interviews
{
    [Authorize(Policy = AuthorizationPolicies.AllRoles)]
    public class IndexModel : PageModel
    {
        private readonly IInterviewService _interviewService;
        private readonly IRoleManagementService _roleManagementService;
        private readonly ILogger<IndexModel> _logger;

        public IndexModel(
            IInterviewService interviewService,
            IRoleManagementService roleManagementService,
            ILogger<IndexModel> logger)
        {
            _interviewService = interviewService;
            _roleManagementService = roleManagementService;
            _logger = logger;
        }

        public IEnumerable<Interview> Interviews { get; set; } = Enumerable.Empty<Interview>();
        public long TotalCount { get; set; }
        public int CurrentPage { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public string CurrentUserRole { get; set; }
        public bool CanCreateInterviews { get; set; }

        [BindProperty(SupportsGet = true)]
        public string SearchTerm { get; set; }

        [BindProperty(SupportsGet = true)]
        public int Page { get; set; } = 1;

        public async Task<IActionResult> OnGetAsync()
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Challenge();
                }

                var userRoles = await _roleManagementService.GetUserRolesAsync(userId);
                CurrentUserRole = userRoles.FirstOrDefault() ?? "";
                
                // Only Standard Users can create interviews
                CanCreateInterviews = userRoles.Contains(UserRoles.User);

                CurrentPage = Math.Max(1, Page);
                var skip = (CurrentPage - 1) * PageSize;

                // Load interviews based on user role and search term
                if (!string.IsNullOrWhiteSpace(SearchTerm))
                {
                    Interviews = await _interviewService.SearchInterviewsAsync(SearchTerm, userId, CurrentUserRole, skip, PageSize);
                    // For search, we'll estimate total count (this could be improved with a dedicated count method)
                    TotalCount = Interviews.Count() == PageSize ? (CurrentPage * PageSize) + 1 : skip + Interviews.Count();
                }
                else
                {
                    // Load interviews based on user role
                    if (userRoles.Contains(UserRoles.Admin))
                    {
                        // Admin can see all interviews (would need a method for this)
                        Interviews = await _interviewService.SearchInterviewsAsync("", userId, CurrentUserRole, skip, PageSize);
                        TotalCount = Interviews.Count(); // Simplified for now
                    }
                    else if (userRoles.Contains(UserRoles.CorpAdmin))
                    {
                        // Corp Admin can see all interviews in their tenant
                        Interviews = await _interviewService.GetInterviewsByTenantAsync(userId, skip, PageSize);
                        TotalCount = await _interviewService.GetInterviewCountByTenantAsync(userId);
                    }
                    else if (userRoles.Contains(UserRoles.User))
                    {
                        // Standard User can see interviews they created
                        Interviews = await _interviewService.GetInterviewsByCreatorAsync(userId, skip, PageSize);
                        TotalCount = await _interviewService.GetInterviewCountByCreatorAsync(userId);
                    }
                    else if (userRoles.Contains(UserRoles.Consultant))
                    {
                        // Consultant can see interviews assigned to them - DISABLED: Consultant functionality removed
                        Interviews = Enumerable.Empty<Interview>();
                        TotalCount = 0;
                    }
                    else
                    {
                        return Forbid();
                    }
                }

                _logger.LogInformation("User {UserId} with role {UserRole} accessed interviews page with {Count} interviews", 
                    userId, CurrentUserRole, Interviews.Count());

                return Page();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading interviews page for user {UserId}", User.Identity?.Name);
                TempData["ErrorMessage"] = "An error occurred while loading interviews. Please try again.";
                return Page();
            }
        }

        public async Task<IActionResult> OnPostUpdateStatusAsync(string interviewId, string status, string notes = null)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return new JsonResult(new { success = false, message = "User not authenticated" });
                }

                if (!Enum.TryParse<InterviewStatus>(status, out var interviewStatus))
                {
                    return new JsonResult(new { success = false, message = "Invalid status" });
                }

                var success = await _interviewService.UpdateInterviewStatusAsync(interviewId, interviewStatus, userId, notes);
                
                if (success)
                {
                    _logger.LogInformation("User {UserId} updated interview {InterviewId} status to {Status}", 
                        userId, interviewId, status);
                    return new JsonResult(new { success = true, message = "Interview status updated successfully" });
                }
                else
                {
                    return new JsonResult(new { success = false, message = "Failed to update interview status" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating interview status for user {UserId}", User.Identity?.Name);
                return new JsonResult(new { success = false, message = "An error occurred while updating the status" });
            }
        }

        public string GetStatusUpdateOptions(InterviewStatus currentStatus)
        {
            var options = new List<string>();

            switch (currentStatus)
            {
                case InterviewStatus.Scheduled:
                    options.AddRange(new[] { "InProgress", "Cancelled", "Rescheduled" });
                    break;
                case InterviewStatus.InProgress:
                    options.AddRange(new[] { "Completed", "Cancelled" });
                    break;
                case InterviewStatus.Rescheduled:
                    options.AddRange(new[] { "Scheduled", "Cancelled" });
                    break;
                // Completed and Cancelled are final states
            }

            return string.Join(",", options);
        }

        public string GetRelativeTime(DateTime dateTime)
        {
            var timeSpan = DateTime.UtcNow - dateTime;
            
            if (timeSpan.TotalDays > 1)
                return $"{(int)timeSpan.TotalDays} days ago";
            if (timeSpan.TotalHours > 1)
                return $"{(int)timeSpan.TotalHours} hours ago";
            if (timeSpan.TotalMinutes > 1)
                return $"{(int)timeSpan.TotalMinutes} minutes ago";
            
            return "Just now";
        }

        public bool IsUpcoming(DateTime interviewDateTime)
        {
            return interviewDateTime > DateTime.UtcNow;
        }

        public bool IsPast(DateTime interviewDateTime)
        {
            return interviewDateTime < DateTime.UtcNow;
        }
    }
}
