using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using RecruiterBot.Core.Constants;
using RecruiterBot.Core.Models;
using RecruiterBot.Infrastructure.Data;
using RecruiterBot.Infrastructure.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RecruiterBot.Infrastructure.Services
{
    public class RoleManagementService : IRoleManagementService
    {
        private readonly UserManager<User> _userManager;
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly IMongoCollection<User> _usersCollection;
        private readonly ILogger<RoleManagementService> _logger;

        public RoleManagementService(
            UserManager<User> userManager,
            RoleManager<IdentityRole> roleManager,
            MongoDbContext context,
            ILogger<RoleManagementService> logger)
        {
            _userManager = userManager;
            _roleManager = roleManager;
            _usersCollection = context.Users;
            _logger = logger;
        }

        public async Task<bool> InitializeRolesAsync()
        {
            try
            {
                foreach (var roleName in UserRoles.AllRoles)
                {
                    if (!await _roleManager.RoleExistsAsync(roleName))
                    {
                        var role = new IdentityRole(roleName);
                        var result = await _roleManager.CreateAsync(role);
                        if (!result.Succeeded)
                        {
                            _logger.LogError("Failed to create role {RoleName}: {Errors}", 
                                roleName, string.Join(", ", result.Errors.Select(e => e.Description)));
                            return false;
                        }
                        _logger.LogInformation("Created role: {RoleName}", roleName);
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing roles");
                return false;
            }
        }

        public async Task<IEnumerable<User>> GetUsersByRoleAsync(string role)
        {
            try
            {
                var users = await _usersCollection
                    .Find(u => u.Roles.Contains(role))
                    .ToListAsync();
                return users;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting users by role {Role}", role);
                return Enumerable.Empty<User>();
            }
        }

        public async Task<bool> AssignRoleToUserAsync(string userId, string role)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null) return false;

                if (!await _userManager.IsInRoleAsync(user, role))
                {
                    var result = await _userManager.AddToRoleAsync(user, role);
                    if (result.Succeeded)
                    {
                        _logger.LogInformation("Assigned role {Role} to user {UserId}", role, userId);
                        return true;
                    }
                    _logger.LogError("Failed to assign role {Role} to user {UserId}: {Errors}", 
                        role, userId, string.Join(", ", result.Errors.Select(e => e.Description)));
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error assigning role {Role} to user {UserId}", role, userId);
                return false;
            }
        }

        public async Task<bool> RemoveRoleFromUserAsync(string userId, string role)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null) return false;

                if (await _userManager.IsInRoleAsync(user, role))
                {
                    var result = await _userManager.RemoveFromRoleAsync(user, role);
                    if (result.Succeeded)
                    {
                        _logger.LogInformation("Removed role {Role} from user {UserId}", role, userId);
                        return true;
                    }
                    _logger.LogError("Failed to remove role {Role} from user {UserId}: {Errors}", 
                        role, userId, string.Join(", ", result.Errors.Select(e => e.Description)));
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing role {Role} from user {UserId}", role, userId);
                return false;
            }
        }

        public async Task<bool> CanUserCreateRole(string currentUserId, string targetRole)
        {
            try
            {
                var currentUserRole = await GetUserPrimaryRoleAsync(currentUserId);
                return UserRoles.CanCreateRole(currentUserRole, targetRole);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if user {UserId} can create role {Role}", currentUserId, targetRole);
                return false;
            }
        }

        public async Task<IEnumerable<string>> GetCreatableRolesForUserAsync(string userId)
        {
            try
            {
                var userRole = await GetUserPrimaryRoleAsync(userId);
                return UserRoles.GetCreatableRoles(userRole);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting creatable roles for user {UserId}", userId);
                return Enumerable.Empty<string>();
            }
        }

        public async Task<string> GetUserPrimaryRoleAsync(string userId)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null) return null;

                var roles = await _userManager.GetRolesAsync(user);
                
                // Return the highest priority role
                if (roles.Contains(UserRoles.Admin)) return UserRoles.Admin;
                if (roles.Contains(UserRoles.CorpAdmin)) return UserRoles.CorpAdmin;
                if (roles.Contains(UserRoles.User)) return UserRoles.User;
                
                return roles.FirstOrDefault();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting primary role for user {UserId}", userId);
                return null;
            }
        }

        public async Task<bool> IsUserInRoleAsync(string userId, string role)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null) return false;

                return await _userManager.IsInRoleAsync(user, role);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if user {UserId} is in role {Role}", userId, role);
                return false;
            }
        }

        public async Task<IEnumerable<User>> GetUsersCreatedByAsync(string creatorId)
        {
            try
            {
                var users = await _usersCollection
                    .Find(u => u.CreatedBy == creatorId)
                    .ToListAsync();
                return users;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting users created by {CreatorId}", creatorId);
                return Enumerable.Empty<User>();
            }
        }

        public async Task<bool> CanUserManageUserAsync(string managerId, string targetUserId)
        {
            try
            {
                var manager = await _userManager.FindByIdAsync(managerId);
                var target = await _userManager.FindByIdAsync(targetUserId);
                
                if (manager == null || target == null) return false;

                var managerRole = await GetUserPrimaryRoleAsync(managerId);
                var targetRole = await GetUserPrimaryRoleAsync(targetUserId);

                // Admin can manage Corp Admin users
                if (managerRole == UserRoles.Admin && targetRole == UserRoles.CorpAdmin)
                    return true;

                // Corp Admin can manage Users they created
                if (managerRole == UserRoles.CorpAdmin && targetRole == UserRoles.User && target.CreatedBy == managerId)
                    return true;

                // Corp Admin can manage Consultants in their tenant (consultants are auto-created, not directly created by Corp Admin)
                if (managerRole == UserRoles.CorpAdmin && targetRole == UserRoles.Consultant && target.Tenant == managerId)
                    return true;

                // Standard Users can manage Consultants they created (through candidate creation)
                if (managerRole == UserRoles.User && targetRole == UserRoles.Consultant && target.CreatedBy == managerId)
                    return true;

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if user {ManagerId} can manage user {TargetId}", managerId, targetUserId);
                return false;
            }
        }

        public async Task<IEnumerable<User>> GetTeamMembersAsync(string corpAdminId)
        {
            try
            {
                // Get all users created by this Corp Admin
                var teamMembers = await _usersCollection
                    .Find(u => u.CreatedBy == corpAdminId)
                    .ToListAsync();

                _logger.LogDebug("Retrieved {Count} team members for Corp Admin {CorpAdminId}", teamMembers.Count, corpAdminId);
                return teamMembers;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting team members for Corp Admin {CorpAdminId}", corpAdminId);
                return Enumerable.Empty<User>();
            }
        }

        public async Task<string> GetCorpAdminForUserAsync(string userId)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null) return null;

                var userRole = await GetUserPrimaryRoleAsync(userId);

                // Admin users don't have a Corp Admin - they are at the top of the hierarchy
                if (userRole == UserRoles.Admin)
                    return null;

                // If user is a Corp Admin, return their own ID
                if (userRole == UserRoles.CorpAdmin)
                    return userId;

                // If user is a standard user, return their creator (Corp Admin)
                if (userRole == UserRoles.User && !string.IsNullOrEmpty(user.CreatedBy))
                {
                    var creator = await _userManager.FindByIdAsync(user.CreatedBy);
                    if (creator != null)
                    {
                        var creatorRole = await GetUserPrimaryRoleAsync(creator.Id);
                        if (creatorRole == UserRoles.CorpAdmin)
                            return creator.Id;
                    }
                }

                // If user is a standard user, return their creator (Corp Admin)
                if (userRole == UserRoles.Consultant && !string.IsNullOrEmpty(user.Tenant))
                {
                    var creator = await _userManager.FindByIdAsync(user.Tenant);
                    if (creator != null)
                    {
                        var creatorRole = await GetUserPrimaryRoleAsync(creator.Id);
                        if (creatorRole == UserRoles.CorpAdmin)
                            return creator.Id;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting Corp Admin for user {UserId}", userId);
                return null;
            }
        }

        public async Task<User?> GetCorpAdminByDomainAsync(string emailDomain)
        {
            try
            {
                var corpAdmins = await GetUsersByRoleAsync(UserRoles.CorpAdmin);
                return corpAdmins.FirstOrDefault(u => u.Email != null &&
                    u.Email.Split('@').Length > 1 &&
                    u.Email.Split('@')[1].Equals(emailDomain, StringComparison.OrdinalIgnoreCase));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting Corp Admin by domain {Domain}", emailDomain);
                return null;
            }
        }

        public async Task<bool> CanCreateCorpAdminForDomainAsync(string email)
        {
            try
            {
                if (string.IsNullOrEmpty(email) || !email.Contains('@'))
                {
                    return false;
                }

                var domain = email.Split('@')[1];
                var existingCorpAdmin = await GetCorpAdminByDomainAsync(domain);

                return existingCorpAdmin == null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if Corp Admin can be created for email {Email}", email);
                return false;
            }
        }

        public async Task<IEnumerable<string>> GetUserRolesAsync(string userId)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                    return Enumerable.Empty<string>();

                return await _userManager.GetRolesAsync(user);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting roles for user {UserId}", userId);
                return Enumerable.Empty<string>();
            }
        }

        public async Task<string> GetCorpAdminIdAsync(string userId)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                    return null;

                var userRoles = await _userManager.GetRolesAsync(user);

                // If user is Corp Admin, return their own ID
                if (userRoles.Contains(UserRoles.CorpAdmin))
                    return userId;

                // If user is Admin, they don't have a Corp Admin
                if (userRoles.Contains(UserRoles.Admin))
                    return null;

                // If user is Admin, they don't have a Corp Admin
                if (userRoles.Contains(UserRoles.User))
                    return user.CreatedBy;

                // For Standard Users and Consultants, return their tenant (which is the Corp Admin ID)
                return user.Tenant;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting Corp Admin ID for user {UserId}", userId);
                return null;
            }
        }

        public async Task<User?> GetUserByIdAsync(string userId)
        {
            try
            {
                return await _userManager.FindByIdAsync(userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user by ID {UserId}", userId);
                return null;
            }
        }

        public async Task<IEnumerable<User>> GetUsersInTenantByRoleAsync(string tenantId, string role)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(tenantId) || string.IsNullOrWhiteSpace(role))
                    return Enumerable.Empty<User>();

                var users = await _usersCollection
                    .Find(u => u.Tenant == tenantId && u.Roles.Contains(role) && u.IsActive)
                    .ToListAsync();

                _logger.LogDebug("Retrieved {Count} users with role {Role} in tenant {TenantId}",
                    users.Count, role, tenantId);

                return users;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting users with role {Role} in tenant {TenantId}", role, tenantId);
                return Enumerable.Empty<User>();
            }
        }
    }
}
