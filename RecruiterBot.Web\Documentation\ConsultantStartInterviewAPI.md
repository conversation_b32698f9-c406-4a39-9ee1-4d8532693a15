# Consultant Start Interview API

## Overview
This API endpoint allows consultants to start an interview session, which updates the interview status to "In Progress" and records the session start time and consultant information.

## Endpoint
```
POST /api/consultant/interviews/{interviewId}/start
```

## Authentication
- Requires JWT authentication with `ApiAuth` scheme
- Requires `Consultant` role authorization

## Parameters
- `interviewId` (path parameter): The ID of the interview to start

## Request Headers
```
Authorization: Bearer {jwt_token}
Content-Type: application/json
```

## Response

### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Interview session started successfully",
  "interview": {
    "id": "string",
    "jobDescription": "string",
    "interviewDateTimeUtc": "2024-01-01T10:00:00Z",
    "interviewDateTimeLocal": "2024-01-01T05:00:00",
    "timeZone": "America/New_York",
    "status": "InProgress",
    "statusDisplayName": "In Progress",
    "notes": "string",
    "createdDateUtc": "2024-01-01T08:00:00Z",
    "sessionStartedDateUtc": "2024-01-01T10:05:00Z",
    "sessionStartedBy": "consultant_user_id",
    "interviewType": "Technical",
    "interviewTypeDisplayName": "Technical",
    "candidate": {
      "id": "string",
      "firstName": "John",
      "lastName": "Doe",
      "fullName": "John Doe",
      "email": "<EMAIL>",
      "phoneNumber": "+**********",
      "skills": ["C#", "JavaScript", "React"],
      "yearsOfExperience": 5,
      "currentJobTitle": "Software Developer",
      "currentCompany": "Tech Corp",
      "summary": "Experienced software developer...",
      "workExperience": [...],
      "education": [...],
      "certifications": [...]
    },
    "llmModel": {
      "id": "string",
      "name": "gpt-4",
      "displayName": "GPT-4",
      "provider": "OpenAI",
      "description": "Advanced language model",
      "modelType": "Paid",
      "maxTokens": 4096
    }
  }
}
```

### Error Responses

#### 400 Bad Request
```json
{
  "success": false,
  "message": "Unable to start interview. Interview may not exist, may not be accessible to you, or may not be in a valid state to start."
}
```

#### 401 Unauthorized
```json
{
  "success": false,
  "message": "User not authenticated"
}
```

#### 500 Internal Server Error
```json
{
  "success": false,
  "message": "An error occurred while starting the interview session"
}
```

## Business Rules
1. Only interviews with status "Scheduled" can be started
2. Consultants can only start interviews assigned to them
3. Interviews can be started up to 1 hour before the scheduled time
4. Starting an interview updates the status to "InProgress" and records:
   - Session start date/time (UTC)
   - Consultant ID who started the session

## Usage Example

### cURL
```bash
curl -X POST "https://api.recruiterbot.com/api/consultant/interviews/60f7b3b3b3b3b3b3b3b3b3b3/start" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"
```

### JavaScript (Fetch)
```javascript
const response = await fetch('/api/consultant/interviews/60f7b3b3b3b3b3b3b3b3b3b3/start', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  }
});

const result = await response.json();
if (result.success) {
  console.log('Interview started:', result.interview);
} else {
  console.error('Error:', result.message);
}
```

## Related Endpoints
- `GET /api/consultant/interviews` - Get list of assigned interviews
- `POST /api/consultant/login` - Authenticate consultant

## Notes
- The response includes complete candidate profile and LLM model information for the interview
- Times are returned in both UTC and the consultant's local timezone
- Session tracking allows for audit trails and interview analytics
