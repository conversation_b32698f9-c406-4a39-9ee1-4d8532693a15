using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using RecruiterBot.Core.Constants;
using RecruiterBot.Core.Models;
using RecruiterBot.Infrastructure.Services;
using RecruiterBot.Web.Models.Api;
using RecruiterBot.Web.Services;
using RecruiterBot.Web.Utilities;
using System.Security.Claims;

namespace RecruiterBot.Web.Controllers.Api
{
    [ApiController]
    [Route("api/consultant")]
    [Produces("application/json")]
    public class ConsultantApiController : ControllerBase
    {
        private readonly UserManager<User> _userManager;
        private readonly SignInManager<User> _signInManager;
        private readonly IJwtService _jwtService;
        private readonly IInterviewService _interviewService;
        private readonly IRoleManagementService _roleManagementService;
        private readonly ILogger<ConsultantApiController> _logger;

        public ConsultantApiController(
            UserManager<User> userManager,
            SignInManager<User> signInManager,
            IJwtService jwtService,
            IInterviewService interviewService,
            IRoleManagementService roleManagementService,
            ILogger<ConsultantApiController> logger)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _jwtService = jwtService;
            _interviewService = interviewService;
            _roleManagementService = roleManagementService;
            _logger = logger;
        }

        /// <summary>
        /// Authenticate consultant and return JWT token
        /// </summary>
        /// <param name="request">Login credentials</param>
        /// <returns>JWT token and user information</returns>
        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<ActionResult<LoginResponse>> Login([FromBody] LoginRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList();

                    return BadRequest(new LoginResponse
                    {
                        Success = false,
                        Message = "Validation failed",
                    });
                }

                _logger.LogInformation("API login attempt for email: {Email}", request.Email);

                // Find user by email
                var user = await _userManager.FindByEmailAsync(request.Email);
                if (user == null)
                {
                    _logger.LogWarning("API login attempt for non-existent user: {Email}", request.Email);
                    return Unauthorized(new LoginResponse
                    {
                        Success = false,
                        Message = "Invalid email or password"
                    });
                }

                // Check if user is active and activated
                if (!user.IsActive || !user.IsActivated)
                {
                    _logger.LogWarning("API login attempt for inactive user: {Email}", request.Email);
                    return Unauthorized(new LoginResponse
                    {
                        Success = false,
                        Message = "Account is not active or not activated"
                    });
                }

                // Check if user is a consultant
                var userRoles = await _userManager.GetRolesAsync(user);
                if (!userRoles.Contains(UserRoles.Consultant))
                {
                    _logger.LogWarning("API login attempt for non-consultant user: {Email}", request.Email);
                    return Unauthorized(new LoginResponse
                    {
                        Success = false,
                        Message = "Access denied. Consultant role required."
                    });
                }

                // Check if user's Corp Admin is active (for non-Admin users)
                if (!userRoles.Contains(UserRoles.Admin))
                {
                    try
                    {
                        var corpAdminId = await _roleManagementService.GetCorpAdminForUserAsync(user.Id);
                        if (!string.IsNullOrEmpty(corpAdminId))
                        {
                            var corpAdmin = await _userManager.FindByIdAsync(corpAdminId);
                            if (corpAdmin != null && !corpAdmin.IsActive)
                            {
                                _logger.LogWarning("API login attempt for user {Email} whose Corp Admin {CorpAdminEmail} is inactive",
                                    request.Email, corpAdmin.Email);
                                return Unauthorized(new LoginResponse
                                {
                                    Success = false,
                                    Message = "Your organization account has been deactivated. Please contact support."
                                });
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error checking Corp Admin status for user {Email}", request.Email);
                        // Continue with login if we can't determine Corp Admin status
                    }
                }

                // Verify password
                var result = await _signInManager.CheckPasswordSignInAsync(user, request.Password, lockoutOnFailure: true);
                if (!result.Succeeded)
                {
                    if (result.IsLockedOut)
                    {
                        _logger.LogWarning("API login attempt for locked out user: {Email}", request.Email);
                        return Unauthorized(new LoginResponse
                        {
                            Success = false,
                            Message = "Account is locked out due to multiple failed login attempts"
                        });
                    }

                    _logger.LogWarning("API login failed for user: {Email}", request.Email);
                    return Unauthorized(new LoginResponse
                    {
                        Success = false,
                        Message = "Invalid email or password"
                    });
                }

                // Generate JWT token
                var token = await _jwtService.GenerateTokenAsync(user);
                var expiresAt = DateTime.UtcNow.AddMinutes(480); // 8 hours

                _logger.LogInformation("API login successful for user: {Email}", request.Email);

                return Ok(new LoginResponse
                {
                    Success = true,
                    Message = "Login successful",
                    Token = token,
                    ExpiresAt = expiresAt,
                    User = new UserInfo
                    {
                        Id = user.Id,
                        Email = user.Email,
                        FirstName = user.FirstName,
                        LastName = user.LastName,
                        Role = userRoles.FirstOrDefault(),
                        TimeZone = user.TimeZone,
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during API login for email: {Email}", request.Email);
                return StatusCode(500, new LoginResponse
                {
                    Success = false,
                    Message = "An error occurred during login"
                });
            }
        }

        /// <summary>
        /// Get all interviews assigned to the authenticated consultant
        /// </summary>
        /// <param name="page">Page number (1-based)</param>
        /// <param name="pageSize">Number of items per page</param>
        /// <param name="includeInactive">Include inactive interviews</param>
        /// <returns>List of interviews assigned to the consultant</returns>
        [HttpGet("interviews")]
        [Authorize(AuthenticationSchemes = "ApiAuth", Policy = AuthorizationPolicies.ConsultantOnly)]
        public async Task<ActionResult<InterviewListResponse>> GetInterviews(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50,
            [FromQuery] bool includeInactive = false)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var userTimeZone = User.FindFirst("timezone")?.Value ?? "UTC";

                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new InterviewListResponse
                    {
                        Success = false,
                        Message = "User not authenticated"
                    });
                }

                _logger.LogInformation("Getting interviews for consultant {UserId}, page {Page}, pageSize {PageSize}", 
                    userId, page, pageSize);

                // Validate pagination parameters
                if (page < 1) page = 1;
                if (pageSize < 1 || pageSize > 100) pageSize = 50;

                var skip = (page - 1) * pageSize;

                // Get interviews for the consultant - DISABLED: Consultant functionality removed
                var interviews = await _interviewService.GetUpcomingInterviewsForConsultantAsync(userId, 7);

                var interviewList = interviews.Take(pageSize).ToList();
                var hasNextPage = interviews.Count() > pageSize;

                // Convert to API response format
                var interviewResponses = interviewList.Select(interview => new InterviewResponse
                {
                    Id = interview.Id,
                    JobDescription = interview.JobDescription,
                    InterviewDateTimeUtc = interview.InterviewDateTimeUtc,
                    InterviewDateTimeLocal = TimeZoneHelper.ConvertFromUtc(interview.InterviewDateTimeUtc, userTimeZone),
                    TimeZone = userTimeZone,
                    Status = interview.Status,
                    StatusDisplayName = interview.StatusDisplayName,
                    Notes = interview.Notes,
                    CreatedDateUtc = interview.CreatedDateUtc,
                    CompletedDateUtc = interview.CompletedDateUtc,
                    CancelledDateUtc = interview.CancelledDateUtc,
                    CancellationReason = interview.CancellationReason,
                    SessionStartedDateUtc = interview.SessionStartedDateUtc,
                    SessionStartedBy = interview.SessionStartedBy,
                    IsUpcoming = interview.IsUpcoming,
                    IsPast = interview.IsPast,
                    Candidate = interview.Candidate != null ? new CandidateInfo
                    {
                        Id = interview.Candidate.Id,
                        FirstName = interview.Candidate.FirstName,
                        LastName = interview.Candidate.LastName,
                        Email = interview.Candidate.Email,
                        PhoneNumber = interview.Candidate.Phone,
                        Skills = interview.Candidate.Skills?.ToList() ?? new List<string>(),
                        YearsOfExperience = interview.Candidate.YearsOfExperience,
                        CurrentJobTitle = interview.Candidate.CurrentTitle,
                        CurrentCompany = interview.Candidate.CurrentCompany
                    } : null,
                    LLMModel = interview.LLMModel != null ? new LLMModelInfo
                    {
                        Id = interview.LLMModel.Id,
                        Name = interview.LLMModel.DisplayName,
                        Provider = interview.LLMModel.ModelType.ToString(),
                        Description = interview.LLMModel.Description
                    } : null
                }).ToList();

                return Ok(new InterviewListResponse
                {
                    Success = true,
                    Message = "Interviews retrieved successfully",
                    Interviews = interviewResponses,
                    TotalCount = interviewResponses.Count(),
                    Page = page,
                    PageSize = pageSize,
                    HasNextPage = hasNextPage,
                    HasPreviousPage = page > 1
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting interviews for consultant");
                return StatusCode(500, new InterviewListResponse
                {
                    Success = false,
                    Message = "An error occurred while retrieving interviews"
                });
            }
        }

        /// <summary>
        /// Start an interview session
        /// </summary>
        /// <param name="interviewId">Interview ID to start</param>
        /// <returns>Interview details with session information</returns>
        [HttpPost("interviews/{interviewId}/start")]
        [Authorize(AuthenticationSchemes = "ApiAuth", Policy = AuthorizationPolicies.ConsultantOnly)]
        public async Task<ActionResult<StartInterviewResponse>> StartInterview(string interviewId)
        {
            try
            {
                var consultantId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var userTimeZone = User.FindFirst("timezone")?.Value ?? "UTC";

                if (string.IsNullOrEmpty(consultantId))
                {
                    return Unauthorized(new StartInterviewResponse
                    {
                        Success = false,
                        Message = "User not authenticated"
                    });
                }

                if (string.IsNullOrEmpty(interviewId))
                {
                    return BadRequest(new StartInterviewResponse
                    {
                        Success = false,
                        Message = "Interview ID is required"
                    });
                }

                _logger.LogInformation("Starting interview session {InterviewId} for consultant {ConsultantId}",
                    interviewId, consultantId);

                // Start the interview session
                var interview = await _interviewService.StartInterviewSessionAsync(interviewId, consultantId);

                if (interview == null)
                {
                    return BadRequest(new StartInterviewResponse
                    {
                        Success = false,
                        Message = "Unable to start interview. Interview may not exist, may not be accessible to you, or may not be in a valid state to start."
                    });
                }

                // Convert to local time for the response
                TimeZoneInfo timeZoneInfo;
                try
                {
                    timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(userTimeZone);
                }
                catch
                {
                    timeZoneInfo = TimeZoneInfo.Utc;
                }

                var localDateTime = TimeZoneInfo.ConvertTimeFromUtc(interview.InterviewDateTimeUtc, timeZoneInfo);

                var response = new StartInterviewResponse
                {
                    Success = true,
                    Message = "Interview session started successfully",
                    Interview = new InterviewSessionInfo
                    {
                        Id = interview.Id,
                        JobDescription = interview.JobDescription,
                        InterviewDateTimeUtc = interview.InterviewDateTimeUtc,
                        InterviewDateTimeLocal = localDateTime,
                        TimeZone = userTimeZone,
                        Status = interview.Status,
                        StatusDisplayName = interview.StatusDisplayName,
                        Notes = interview.Notes,
                        CreatedDateUtc = interview.CreatedDateUtc,
                        SessionStartedDateUtc = interview.SessionStartedDateUtc,
                        SessionStartedBy = interview.SessionStartedBy,
                        InterviewType = interview.InterviewType,
                        InterviewTypeDisplayName = interview.InterviewTypeDisplayName,
                        Candidate = interview.Candidate != null ? new CandidateProfile
                        {
                            Id = interview.Candidate.Id,
                            FirstName = interview.Candidate.FirstName,
                            LastName = interview.Candidate.LastName,
                            Email = interview.Candidate.Email,
                            PhoneNumber = interview.Candidate.Phone,
                            Skills = interview.Candidate.Skills?.ToList() ?? new List<string>(),
                            YearsOfExperience = interview.Candidate.YearsOfExperience,
                            CurrentJobTitle = interview.Candidate.CurrentTitle,
                            CurrentCompany = interview.Candidate.CurrentCompany,
                            Summary = interview.Candidate.Summary,
                            WorkExperience = interview.Candidate.WorkExperience?.ToList() ?? new List<WorkExperience>(),
                            Education = interview.Candidate.Education?.ToList() ?? new List<Education>(),
                            Certifications = interview.Candidate.Certifications?.ToList() ?? new List<string>()
                        } : null,
                        LLMModel = interview.LLMModel != null ? new LLMModelProfile
                        {
                            Id = interview.LLMModel.Id,
                            Name = interview.LLMModel.ModelName,
                            DisplayName = interview.LLMModel.DisplayName,
                            Provider = interview.LLMModel.ModelProvider.ToString(),
                            Description = interview.LLMModel.Description,
                            ModelType = interview.LLMModel.ModelType,
                            MaxTokens = interview.LLMModel.MaxTokens
                        } : null
                    }
                };

                _logger.LogInformation("Interview session {InterviewId} started successfully for consultant {ConsultantId}",
                    interviewId, consultantId);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting interview session {InterviewId} for consultant", interviewId);
                return StatusCode(500, new StartInterviewResponse
                {
                    Success = false,
                    Message = "An error occurred while starting the interview session"
                });
            }
        }
    }
}
