using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using RecruiterBot.Core.Constants;
using RecruiterBot.Core.Models;

namespace RecruiterBot.Infrastructure.Services
{
    /// <summary>
    /// Service for registering candidates as consultant users.
    ///
    /// IMPORTANT: When a candidate is registered as a consultant user, the User ID
    /// is set to match the Candidate ID to maintain a 1:1 relationship between
    /// candidates and their corresponding consultant user accounts.
    /// </summary>
    public class ConsultantRegistrationService : IConsultantRegistrationService
    {
        private readonly UserManager<User> _userManager;
        private readonly IRoleManagementService _roleManagementService;
        private readonly IPasswordGeneratorService _passwordGeneratorService;
        private readonly IEmailService _emailService;
        private readonly ILogger<ConsultantRegistrationService> _logger;

        public ConsultantRegistrationService(
            UserManager<User> userManager,
            IRoleManagementService roleManagementService,
            IPasswordGeneratorService passwordGeneratorService,
            IEmailService emailService,
            ILogger<ConsultantRegistrationService> logger)
        {
            _userManager = userManager;
            _roleManagementService = roleManagementService;
            _passwordGeneratorService = passwordGeneratorService;
            _emailService = emailService;
            _logger = logger;
        }

        public async Task<User?> RegisterCandidateAsConsultantAsync(Candidate candidate, string createdByUserId)
        {
            if (candidate == null)
                throw new ArgumentNullException(nameof(candidate));

            if (string.IsNullOrWhiteSpace(createdByUserId))
                throw new ArgumentException("Created by user ID cannot be empty", nameof(createdByUserId));

            if (string.IsNullOrWhiteSpace(candidate.Email))
            {
                _logger.LogWarning("Cannot register candidate {CandidateId} as consultant: no email address", candidate.Id);
                return null;
            }

            try
            {
                // Check if user already exists by email
                if (await UserExistsAsync(candidate.Email))
                {
                    _logger.LogInformation("User with email {Email} already exists, skipping consultant registration", candidate.Email);
                    return null;
                }

                // Check if user already exists with the candidate's ID
                var existingUserById = await _userManager.FindByIdAsync(candidate.Id);
                if (existingUserById != null)
                {
                    _logger.LogInformation("User with ID {UserId} already exists, skipping consultant registration for candidate {CandidateId}",
                        candidate.Id, candidate.Id);
                    return null;
                }

                // Get the Corp Admin ID for the user who created the candidate
                var corpAdminId = await _roleManagementService.GetCorpAdminForUserAsync(createdByUserId);
                if (string.IsNullOrWhiteSpace(corpAdminId))
                {
                    _logger.LogError("Could not determine Corp Admin for user {UserId}", createdByUserId);
                    return null;
                }

                // Generate temporary password and activation token
                var temporaryPassword = _passwordGeneratorService.GenerateTemporaryPassword();
                var activationToken = _passwordGeneratorService.GenerateActivationToken();

                // Create the consultant user with the same ID as the candidate
                var consultantUser = new User
                {
                    Id = candidate.Id, // Use the same ID as the candidate
                    UserName = candidate.Email,
                    Email = candidate.Email,
                    FirstName = candidate.FirstName ?? "Unknown",
                    LastName = candidate.LastName ?? "User",
                    CreatedBy = createdByUserId,
                    Tenant = corpAdminId, // Associate with the same Corp Admin
                    EmailConfirmed = false, // Require activation
                    MustChangePassword = true,
                    IsTemporaryPassword = true,
                    ActivationToken = activationToken,
                    ActivationTokenExpiry = DateTime.UtcNow.AddHours(24),
                    IsActivated = false
                };

                // Verify that the candidate ID is a valid ObjectId before proceeding
                if (!MongoDB.Bson.ObjectId.TryParse(candidate.Id, out _))
                {
                    _logger.LogError("Candidate {CandidateId} has invalid ObjectId format, cannot create consultant user with matching ID", candidate.Id);
                    return null;
                }

                // Create the user account
                var result = await _userManager.CreateAsync(consultantUser, temporaryPassword);
                if (!result.Succeeded)
                {
                    _logger.LogError("Failed to create consultant user for candidate {CandidateId}: {Errors}",
                        candidate.Id, string.Join(", ", result.Errors.Select(e => e.Description)));
                    return null;
                }

                // Assign CONSULTANT role
                var roleAssigned = await _roleManagementService.AssignRoleToUserAsync(consultantUser.Id, UserRoles.Consultant);
                if (!roleAssigned)
                {
                    _logger.LogError("Failed to assign CONSULTANT role to user {UserId}", consultantUser.Id);
                    // Clean up - delete the user if role assignment failed
                    await _userManager.DeleteAsync(consultantUser);
                    return null;
                }

                _logger.LogInformation("Successfully created consultant user {UserId} for candidate {CandidateId} (IDs are identical)",
                    consultantUser.Id, candidate.Id);

                // Send activation email
                await SendConsultantActivationEmailAsync(consultantUser, temporaryPassword, activationToken);

                return consultantUser;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error registering candidate {CandidateId} as consultant", candidate.Id);
                return null;
            }
        }

        public async Task<bool> UserExistsAsync(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            try
            {
                var user = await _userManager.FindByEmailAsync(email);
                return user != null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if user exists with email {Email}", email);
                return false;
            }
        }

        /// <summary>
        /// Gets the consultant user associated with a candidate ID.
        /// Since consultant users have the same ID as their corresponding candidate,
        /// this method simply looks up the user by the candidate ID.
        /// </summary>
        /// <param name="candidateId">The candidate ID</param>
        /// <returns>The consultant user if found, null otherwise</returns>
        public async Task<User> GetConsultantUserByCandidateIdAsync(string candidateId)
        {
            if (string.IsNullOrWhiteSpace(candidateId))
                return null;

            try
            {
                var user = await _userManager.FindByIdAsync(candidateId);

                // Verify that this user is actually a consultant
                if (user != null)
                {
                    var roles = await _userManager.GetRolesAsync(user);
                    if (!roles.Contains(UserRoles.Consultant))
                    {
                        _logger.LogWarning("User {UserId} exists but is not a consultant", candidateId);
                        return null;
                    }
                }

                return user;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting consultant user for candidate {CandidateId}", candidateId);
                return null;
            }
        }

        public async Task<User?> GetConsultantByEmailAsync(string candidateEmail)
        {
            if (string.IsNullOrWhiteSpace(candidateEmail))
                return null;

            try
            {
                var user = await _userManager.FindByEmailAsync(candidateEmail);
                if (user != null && await _roleManagementService.IsUserInRoleAsync(user.Id, UserRoles.Consultant))
                {
                    return user;
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting consultant by email {Email}", candidateEmail);
                return null;
            }
        }

        private async Task SendConsultantActivationEmailAsync(User consultantUser, string temporaryPassword, string activationToken)
        {
            try
            {
                // Note: We would need access to HttpContext or a URL helper to generate the activation URL
                // For now, we'll use a placeholder URL structure that should be updated based on the application's routing
                var activationUrl = $"https://localhost:5213/Account/SetupAccount?userId={consultantUser.Id}&token={activationToken}";

                var emailSent = await _emailService.SendAccountActivationAsync(
                    consultantUser.Email,
                    consultantUser.FirstName,
                    temporaryPassword,
                    activationUrl,
                    UserRoles.Consultant);

                if (emailSent)
                {
                    _logger.LogInformation("Consultant activation email sent to {Email}", consultantUser.Email);
                }
                else
                {
                    _logger.LogError("Failed to send consultant activation email to {Email}", consultantUser.Email);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending consultant activation email to {Email}", consultantUser.Email);
            }
        }
    }
}
