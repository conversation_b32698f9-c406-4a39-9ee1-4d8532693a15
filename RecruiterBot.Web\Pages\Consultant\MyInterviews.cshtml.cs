using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Logging;
using RecruiterBot.Infrastructure.Services;
using RecruiterBot.Core.Models;
using RecruiterBot.Core.Constants;
using System.Security.Claims;

namespace RecruiterBot.Web.Pages.Consultant
{
    [Authorize(Policy = AuthorizationPolicies.ConsultantOnly)]
    public class MyInterviewsModel : PageModel
    {
        private readonly IInterviewService _interviewService;
        private readonly ILogger<MyInterviewsModel> _logger;

        public MyInterviewsModel(
            IInterviewService interviewService,
            ILogger<MyInterviewsModel> logger)
        {
            _interviewService = interviewService;
            _logger = logger;
        }

        public IEnumerable<Interview> Interviews { get; set; } = Enumerable.Empty<Interview>();
        public IEnumerable<Interview> UpcomingInterviews { get; set; } = Enumerable.Empty<Interview>();
        public long TotalCount { get; set; }
        public int CurrentPage { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);

        [BindProperty(SupportsGet = true)]
        public string SearchTerm { get; set; }

        [BindProperty(SupportsGet = true)]
        public int Page { get; set; } = 1;

        [BindProperty(SupportsGet = true)]
        public string StatusFilter { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Challenge();
                }

                CurrentPage = Math.Max(1, Page);
                var skip = (CurrentPage - 1) * PageSize;

                // Get upcoming interviews (next 7 days) - DISABLED: Consultant functionality removed
                UpcomingInterviews = await _interviewService.GetUpcomingInterviewsForConsultantAsync(userId, 7);

                // Load interviews based on search term and filters
                if (!string.IsNullOrWhiteSpace(SearchTerm))
                {
                    Interviews = await _interviewService.SearchInterviewsAsync(SearchTerm, userId, UserRoles.Consultant, skip, PageSize);
                    // For search, we'll estimate total count
                    TotalCount = Interviews.Count() == PageSize ? (CurrentPage * PageSize) + 1 : skip + Interviews.Count();
                }
                else
                {
                    // Get all interviews assigned to this consultant
                    Interviews = await _interviewService.GetInterviewsByConsultantAsync(userId, skip, PageSize);
                    TotalCount = await _interviewService.GetInterviewCountByConsultantAsync(userId);
                }

                // Apply status filter if specified
                if (!string.IsNullOrWhiteSpace(StatusFilter) && Enum.TryParse<InterviewStatus>(StatusFilter, out var status))
                {
                    Interviews = Interviews.Where(i => i.Status == status);
                    // Recalculate count for filtered results (simplified)
                    TotalCount = Interviews.Count();
                }

                _logger.LogInformation("Consultant {UserId} accessed their interviews page with {Count} interviews", 
                    userId, Interviews.Count());

                return Page();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading consultant interviews page for user {UserId}", User.Identity?.Name);
                TempData["ErrorMessage"] = "An error occurred while loading your interviews. Please try again.";
                return Page();
            }
        }

        public async Task<IActionResult> OnPostUpdateStatusAsync(string interviewId, string status, string notes = null)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return new JsonResult(new { success = false, message = "User not authenticated" });
                }

                if (!Enum.TryParse<InterviewStatus>(status, out var interviewStatus))
                {
                    return new JsonResult(new { success = false, message = "Invalid status" });
                }

                var success = await _interviewService.UpdateInterviewStatusAsync(interviewId, interviewStatus, userId, notes);
                
                if (success)
                {
                    _logger.LogInformation("Consultant {UserId} updated interview {InterviewId} status to {Status}", 
                        userId, interviewId, status);
                    return new JsonResult(new { success = true, message = "Interview status updated successfully" });
                }
                else
                {
                    return new JsonResult(new { success = false, message = "Failed to update interview status" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating interview status for consultant {UserId}", User.Identity?.Name);
                return new JsonResult(new { success = false, message = "An error occurred while updating the status" });
            }
        }

        public string GetStatusUpdateOptions(InterviewStatus currentStatus)
        {
            var options = new List<string>();

            switch (currentStatus)
            {
                case InterviewStatus.Scheduled:
                    options.AddRange(new[] { "InProgress", "Cancelled", "Rescheduled" });
                    break;
                case InterviewStatus.InProgress:
                    options.AddRange(new[] { "Completed", "Cancelled" });
                    break;
                case InterviewStatus.Rescheduled:
                    options.AddRange(new[] { "Scheduled", "Cancelled" });
                    break;
                // Completed and Cancelled are final states
            }

            return string.Join(",", options);
        }

        public string GetRelativeTime(DateTime dateTime)
        {
            var timeSpan = DateTime.UtcNow - dateTime;
            
            if (timeSpan.TotalDays > 1)
                return $"{(int)timeSpan.TotalDays} days ago";
            if (timeSpan.TotalHours > 1)
                return $"{(int)timeSpan.TotalHours} hours ago";
            if (timeSpan.TotalMinutes > 1)
                return $"{(int)timeSpan.TotalMinutes} minutes ago";
            
            return "Just now";
        }

        public bool IsUpcoming(DateTime interviewDateTime)
        {
            return interviewDateTime > DateTime.UtcNow;
        }

        public bool IsPast(DateTime interviewDateTime)
        {
            return interviewDateTime < DateTime.UtcNow;
        }

        public string GetTimeUntilInterview(DateTime interviewDateTime)
        {
            var timeSpan = interviewDateTime - DateTime.UtcNow;
            
            if (timeSpan.TotalDays >= 1)
                return $"in {(int)timeSpan.TotalDays} day{((int)timeSpan.TotalDays != 1 ? "s" : "")}";
            if (timeSpan.TotalHours >= 1)
                return $"in {(int)timeSpan.TotalHours} hour{((int)timeSpan.TotalHours != 1 ? "s" : "")}";
            if (timeSpan.TotalMinutes >= 1)
                return $"in {(int)timeSpan.TotalMinutes} minute{((int)timeSpan.TotalMinutes != 1 ? "s" : "")}";
            if (timeSpan.TotalSeconds > 0)
                return "starting soon";
            
            return "overdue";
        }
    }
}
